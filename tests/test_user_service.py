import pytest
import json
from unittest.mock import AsyncMock, patch
import httpx
from base.hanhai_user.user_service_client import UserServiceClient, get_user_name_map_by_ids
from base.hanhai_user.config import UserServiceConfig
from base.hanhai_user.user_info_injector import UserInfoInjector


class TestUserServiceClient:
    """用户服务客户端测试"""
    
    @pytest.fixture
    def config(self):
        """测试配置"""
        return UserServiceConfig(host="http://test-host")
    
    @pytest.fixture
    def client(self, config):
        """测试客户端"""
        return UserServiceClient(config)
    
    @pytest.mark.asyncio
    async def test_get_user_name_map_by_ids_success(self, client):
        """测试成功获取用户信息"""
        # Mock API响应
        mock_response_data = [
            {"nickName": "Alice", "userId": 1, "userName": "alice"},
            {"nickName": "Bob", "userId": 3, "userName": "bob"}
        ]

        # 创建一个mock response对象
        mock_response = AsyncMock()
        mock_response.json.return_value = mock_response_data
        mock_response.raise_for_status.return_value = None

        # 创建一个mock client
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response

        # Mock AsyncClient的context manager
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client_class.return_value.__aexit__.return_value = None

            # 执行测试
            result = await client.get_user_name_map_by_ids(["1", "3"])

            # 验证结果
            expected = {"1": "alice", "3": "bob"}
            assert result == expected
    
    @pytest.mark.asyncio
    async def test_get_user_name_map_by_ids_empty_list(self, client):
        """测试空用户ID列表"""
        result = await client.get_user_name_map_by_ids([])
        assert result == {}
    
    @pytest.mark.asyncio
    async def test_get_user_name_map_by_ids_http_error(self, client):
        """测试HTTP错误处理"""
        with patch('httpx.AsyncClient') as mock_client:
            # 配置mock抛出HTTP错误
            mock_response = AsyncMock()
            mock_response.status_code = 500
            mock_response.text = "Internal Server Error"
            
            mock_client.return_value.__aenter__.return_value.get.side_effect = httpx.HTTPStatusError(
                "Server Error", request=None, response=mock_response
            )
            
            # 执行测试
            result = await client.get_user_name_map_by_ids(["1", "2"])
            
            # 验证返回默认值
            expected = {"1": "未知用户", "2": "未知用户"}
            assert result == expected
    
    @pytest.mark.asyncio
    async def test_get_user_name_map_by_ids_network_error(self, client):
        """测试网络错误处理"""
        with patch('httpx.AsyncClient') as mock_client:
            # 配置mock抛出网络错误
            mock_client.return_value.__aenter__.return_value.get.side_effect = httpx.RequestError(
                "Network error"
            )
            
            # 执行测试
            result = await client.get_user_name_map_by_ids(["1", "2"])
            
            # 验证返回默认值
            expected = {"1": "未知用户", "2": "未知用户"}
            assert result == expected
    
    @pytest.mark.asyncio
    async def test_get_user_name_map_by_ids_use_nickname_fallback(self, client):
        """测试当userName为空时使用nickName"""
        # Mock API响应，userName为空
        mock_response_data = [
            {"nickName": "Alice Nickname", "userId": 1, "userName": ""},
            {"nickName": "Bob Nickname", "userId": 2, "userName": None}
        ]

        # 创建一个mock response对象
        mock_response = AsyncMock()
        mock_response.json.return_value = mock_response_data
        mock_response.raise_for_status.return_value = None

        # 创建一个mock client
        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response

        # Mock AsyncClient的context manager
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client_class.return_value.__aexit__.return_value = None

            # 执行测试
            result = await client.get_user_name_map_by_ids(["1", "2"])

            # 验证使用nickName作为fallback
            expected = {"1": "Alice Nickname", "2": "Bob Nickname"}
            assert result == expected


class TestUserInfoInjector:
    """用户信息注入器测试"""
    
    @pytest.mark.asyncio
    async def test_inject_user_names_with_real_api(self):
        """测试使用真实API进行用户信息注入"""
        # 准备测试数据
        test_data = [
            {"id": 1, "created_user": "1", "name": "Task 1"},
            {"id": 2, "created_user": "3", "name": "Task 2"}
        ]
        
        field_mappings = {"created_user": "created_user_name"}
        
        # Mock get_user_name_map_by_ids函数
        with patch('base.hanhai_user.user_info_injector.get_user_name_map_by_ids') as mock_get_users:
            mock_get_users.return_value = {"1": "Alice", "3": "Bob"}
            
            # 执行测试
            result = await UserInfoInjector.inject_user_names(test_data, field_mappings)
            
            # 验证结果
            assert result[0]["created_user_name"] == "Alice"
            assert result[1]["created_user_name"] == "Bob"
            
            # 验证调用参数（由于set的无序性，需要检查调用的参数包含正确的用户ID）
            mock_get_users.assert_called_once()
            called_args = mock_get_users.call_args[0][0]
            assert set(called_args) == {"1", "3"}


@pytest.mark.asyncio
async def test_convenience_function():
    """测试便捷函数"""
    with patch('base.hanhai_user.user_service_client.default_user_service_client.get_user_name_map_by_ids') as mock_method:
        mock_method.return_value = {"1": "Test User"}
        
        result = await get_user_name_map_by_ids(["1"])
        
        assert result == {"1": "Test User"}
        mock_method.assert_called_once_with(["1"])
