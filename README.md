



# 项目说明

### 1. 源码目录说明:
1. hanhai_resource 瀚海资源对接
2. hanhai_space 瀚海空间对接
3. hanhai_user 瀚海用户对接
4. tests 测试

### 2. 创建Python环境，安装相关依赖：

   需要提前安装conda进行python环境管理

   ```shell
   conda create --name hanhai_sdk python=3.10 -y
   conda activate hanhai_sdk
   pip install -r requirements.txt
   
   pip list | grep pytest
   ```
   
### 3. 单元测试

核心基本流程最好有单元测试，使用pytest测试框架，所以最好符合该框架测试规范


### 4. 发布sdk
```shell
# 安装构建工具
pip install setuptools wheel

# 生成分发包
python setup.py sdist bdist_wheel
dist/
├── base_matrix_python_sdk-3.1.0-py3-none-any.whl  # 二进制包
└── base_matrix_python_sdk-3.1.0.tar.gz             # 源码包

# 本地测试安装
pip install ./dist/base_matrix_python_sdk-3.1.0-py3-none-any.whl
# 或在其他项目中测试base_matrix_python_sdk ,应该如何写如下命令
pip install -e /Users/<USER>/workspace/go_workspace/CICD_OPS/Base-Matrix-pyhton-sdk

pip list | grep base-matrix-python-sdk                                                
base-matrix-python-sdk   3.1.0                 /Users/<USER>/workspace/go_workspace/CICD_OPS/Base-Matrix-pyhton-sdk

```

# sdk 使用

### 1. 为表A 添加过滤字段 b 使用示例

1. 获取header ，设置 space_id
```python
# 中间件：设置请求级别的space_id
@app.middleware("http")
async def set_space_id_context(request: Request, call_next):
    # 从请求头获取space_id
    space_id = request.headers.get("spaceId")
    
    if not currentSpaceId:
        return INVALID_SPACE_ID_RESPONSE
   
    try:
        # 如果已有中间件，可以直接用这句赋值即可
        global_space_id_ctx.set(currentSpaceId)
        return await call_next(request)
    except ValueError:
        return JSONResponse(
            {"error": "Invalid X-Space-ID format"},
            status_code=400
        )
```


2. 确保会执行 生命周期管理器
```python
if __name__ == "__main__":
    # ... ...
    # 8. 启动应用
    uvicorn.run(app, host=args.host, port=args.port, access_log=False,
                # 确保生命周期管理器在应用启动时执行
                lifespan="on"
                )
                
```

3. 指定需处理的表和字段


3.1 单表、多表过滤同一个字段


```python
from base.hanhai_space.space_id_filter import SpaceIdFilter
from base.hanhai_space.space_id_context import global_space_id_ctx

# 注册事件监听器（应用启动时执行）
@asynccontextmanager
async def lifespan(app: fastapi.FastAPI):
    """应用生命周期管理器"""
    logger.info("=== 应用启动 ===")
    space_filter = SpaceIdFilter(
        table_name="task",  # 需处理的表，单表"task" 、多表 ["task", "project", "dataset"]
        column_name="space_id", # 需过滤的字段
        get_value_fn=lambda: global_space_id_ctx.get(),
        debug=True  # 生产环境设为False
    )
    # 注册过滤器
    space_filter.register(database.engine.sync_engine)
    yield
    logger.info("=== 应用关闭 ===")

```

3.2 不同表使用不同过滤字段
```python
from base.hanhai_space.space_id_filter import SpaceIdFilter
from base.hanhai_space.space_id_context import global_space_id_ctx

# 注册事件监听器（应用启动时执行）
@asynccontextmanager
async def lifespan(app: fastapi.FastAPI):
   """应用生命周期管理器"""
    logger.info("=== 应用启动 ===")
   # 需要创建多个过滤器
   task_filter = SpaceIdFilter(
       table_name="task",
       column_name="space_id",
       get_value_fn=lambda: global_space_id_ctx.get()
   )
   
   project_filter = SpaceIdFilter(
       table_name="project",
       column_name="tenant_id",  # 不同字段名
       get_value_fn=lambda: global_space_id_ctx.get()
   )
   
   # 分别注册
   task_filter.register(engine)
   project_filter.register(engine)
   yield
   logger.info("=== 应用关闭 ===")

```

### 2. 用户id转换为用户名 使用示例

#### 2.1 基本使用

```python
from base.hanhai_user import get_user_name_map_by_ids

# 获取用户名映射
user_ids = ["1", "2", "3"]
user_name_map = await get_user_name_map_by_ids(user_ids)
print(user_name_map)  # {'1': 'alice', '2': 'bob', '3': 'charlie'}
```

#### 2.2 配置API服务地址

```python
from base.hanhai_user import UserServiceConfig, UserServiceClient

# 方式1：通过环境变量配置
import os
os.environ['HANHAI_USER_SERVICE_HOST'] = 'http://your-api-host:8080'

# 方式2：直接配置
config = UserServiceConfig(host="http://your-api-host:8080")
client = UserServiceClient(config)
user_name_map = await client.get_user_name_map_by_ids(["1", "2"])
```

#### 2.3 使用装饰器自动注入用户信息

```python
from base.hanhai_user import inject_user_info

@inject_user_info({"created_user": "created_user_name", "updated_user": "updated_user_name"})
async def get_tasks():
    # 返回任务列表，装饰器会自动注入用户名
    return [
        {"id": 1, "name": "任务1", "created_user": "1", "updated_user": "2"},
        {"id": 2, "name": "任务2", "created_user": "2", "updated_user": "3"}
    ]

# 调用后会自动添加 created_user_name 和 updated_user_name 字段
tasks = await get_tasks()
```

#### 2.4 手动注入用户信息

```python
from base.hanhai_user import UserInfoInjector

# 列表数据注入
data = [{"created_user": "1"}, {"created_user": "2"}]
field_mappings = {"created_user": "created_user_name"}
result = await UserInfoInjector.inject_user_names(data, field_mappings)

# 单条数据注入
single_data = {"created_user": "1", "name": "任务"}
result = await UserInfoInjector.inject_user_names_single(single_data, field_mappings)
```

#### 2.5 API接口说明

调用的远程API接口：
```
GET {host}/com.kingsoft.ai.hanhai.basematrix.system.api.RemoteUserService/querySimpleUserInfoByIds?userIds=[1,3]
```

响应格式：
```json
[
    {
        "nickName": "string",
        "userId": 0,
        "userName": "string"
    }
]
```

#### 2.6 错误处理

当API调用失败时，系统会自动返回默认值"未知用户"，确保业务流程不被中断。