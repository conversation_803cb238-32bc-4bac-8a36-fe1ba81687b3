"""
用户服务使用示例

这个示例展示了如何使用 get_user_name_map_by_ids 方法来获取用户信息。
"""

import asyncio
import os
import sys
sys.path.append('..')

from base.hanhai_user import get_user_name_map_by_ids, UserServiceClient, UserServiceConfig
from base.hanhai_user.user_info_injector import UserInfoInjector


async def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 直接使用便捷函数
    user_ids = ["1", "2", "3"]
    user_name_map = await get_user_name_map_by_ids(user_ids)
    
    print(f"用户ID列表: {user_ids}")
    print(f"用户名映射: {user_name_map}")
    print()


async def example_custom_config():
    """自定义配置示例"""
    print("=== 自定义配置示例 ===")
    
    # 创建自定义配置
    config = UserServiceConfig(host="http://your-api-host:8080")
    client = UserServiceClient(config)
    
    user_ids = ["1", "2"]
    user_name_map = await client.get_user_name_map_by_ids(user_ids)
    
    print(f"自定义主机: {config.host}")
    print(f"用户ID列表: {user_ids}")
    print(f"用户名映射: {user_name_map}")
    print()


async def example_user_info_injection():
    """用户信息注入示例"""
    print("=== 用户信息注入示例 ===")
    
    # 模拟数据库查询结果
    task_data = [
        {"id": 1, "name": "任务1", "created_user": "1", "updated_user": "2"},
        {"id": 2, "name": "任务2", "created_user": "2", "updated_user": "3"},
        {"id": 3, "name": "任务3", "created_user": "3", "updated_user": "1"}
    ]
    
    # 定义字段映射
    field_mappings = {
        "created_user": "created_user_name",
        "updated_user": "updated_user_name"
    }
    
    print("原始数据:")
    for task in task_data:
        print(f"  {task}")
    
    # 注入用户信息
    enriched_data = await UserInfoInjector.inject_user_names(task_data, field_mappings)
    
    print("\n注入用户信息后:")
    for task in enriched_data:
        print(f"  {task}")
    print()


async def example_single_record_injection():
    """单条记录用户信息注入示例"""
    print("=== 单条记录用户信息注入示例 ===")
    
    # 单条记录
    task = {"id": 1, "name": "重要任务", "created_user": "1", "assigned_user": "2"}
    
    field_mappings = {
        "created_user": "created_user_name",
        "assigned_user": "assigned_user_name"
    }
    
    print("原始记录:")
    print(f"  {task}")
    
    # 注入用户信息
    enriched_task = await UserInfoInjector.inject_user_names_single(task, field_mappings)
    
    print("\n注入用户信息后:")
    print(f"  {enriched_task}")
    print()


def example_environment_config():
    """环境变量配置示例"""
    print("=== 环境变量配置示例 ===")
    
    # 设置环境变量（在实际使用中，这应该在系统环境中设置）
    os.environ['HANHAI_USER_SERVICE_HOST'] = 'http://production-api:8080'
    
    # 使用默认配置（会读取环境变量）
    config = UserServiceConfig()
    print(f"从环境变量读取的主机: {config.host}")
    print(f"API URL: {config.get_user_info_url()}")
    print()


async def main():
    """主函数"""
    print("瀚海用户服务 SDK 使用示例\n")
    
    # 环境变量配置示例
    example_environment_config()
    
    # 基本使用示例
    await example_basic_usage()
    
    # 自定义配置示例
    await example_custom_config()
    
    # 用户信息注入示例
    await example_user_info_injection()
    
    # 单条记录注入示例
    await example_single_record_injection()
    
    print("所有示例执行完成！")


if __name__ == "__main__":
    asyncio.run(main())
