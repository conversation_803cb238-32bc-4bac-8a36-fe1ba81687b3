import logging
from typing import List, Dict, Any
from .user_service_client import get_user_name_map_by_ids

logger = logging.getLogger("plaintext")


class UserInfoInjector:
    @staticmethod
    async def inject_user_names(data: List[Dict[str, Any]], field_mappings: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        为列表数据注入用户姓名
        
        Args:
            data: 需要注入的数据列表
            field_mappings: 字段映射，格式：{"source_field": "target_field"}
                例如：{"created_user": "created_user_name", "updated_user": "updated_user_name"}
                
        Returns:
            注入用户姓名后的数据列表
        """
        logger.info(f"开始用户信息注入，数据条数: {len(data)}, 字段映射: {field_mappings}")
        
        if not data or not field_mappings:
            logger.info("数据为空或字段映射为空，跳过用户信息注入")
            return data
        
        # 收集所有需要查询的user_id
        logger.debug(f"数据内容: {data}")
        logger.debug(f"字段映射: {field_mappings.keys()}")

        user_ids = set()
        for item in data:
            for source_field in field_mappings.keys():
                # 支持字典和对象两种数据格式
                if isinstance(item, dict):
                    # 字典格式
                    if source_field in item and item[source_field]:
                        user_ids.add(str(item[source_field]))
                        logger.debug(f"从字典中找到用户ID: {source_field}={item[source_field]}")
                else:
                    # 对象格式
                    if hasattr(item, source_field):
                        value = getattr(item, source_field)
                        if value:
                            user_ids.add(str(value))
                            logger.debug(f"从对象中找到用户ID: {source_field}={value}")
        
        logger.info(f"收集到的用户ID: {user_ids}")

        
        if not user_ids:
            logger.info("未找到需要查询的用户ID，跳过用户信息注入")
            return data
        
        # 批量获取用户姓名映射
        user_name_map = await get_user_name_map_by_ids(list(user_ids))
        
        logger.info(f"获取到的用户姓名映射: {user_name_map}")
        
        # 注入用户姓名
        injected_count = 0
        for item in data:
            for source_field, target_field in field_mappings.items():
                # 支持字典和对象两种数据格式
                if isinstance(item, dict):
                    # 字典格式
                    if source_field in item and item[source_field]:
                        user_id = str(item[source_field])
                        user_name = user_name_map.get(user_id, "未知用户")
                        item[target_field] = user_name
                        injected_count += 1
                        logger.debug(f"注入用户信息到字典: {source_field}={user_id} -> {target_field}={user_name}")
                else:
                    # 对象格式
                    if hasattr(item, source_field):
                        value = getattr(item, source_field)
                        if value:
                            user_id = str(value)
                            user_name = user_name_map.get(user_id, "未知用户")
                            setattr(item, target_field, user_name)
                            injected_count += 1
                            logger.debug(f"注入用户信息到对象: {source_field}={user_id} -> {target_field}={user_name}")
        
        logger.info(f"用户信息注入完成，共注入 {injected_count} 个字段")
        return data
    
    @staticmethod
    async def inject_user_names_single(data: Dict[str, Any], field_mappings: Dict[str, str]) -> Dict[str, Any]:
        """
        为单个字典数据注入用户姓名
        
        Args:
            data: 需要注入的数据字典
            field_mappings: 字段映射，格式：{"source_field": "target_field"}
                例如：{"created_user": "created_user_name", "updated_user": "updated_user_name"}
                
        Returns:
            注入用户姓名后的数据字典
        """
        logger.info(f"开始单个数据用户信息注入，字段映射: {field_mappings}")
        
        if not data or not field_mappings:
            logger.info("数据为空或字段映射为空，跳过用户信息注入")
            return data
        
        # 收集需要查询的user_id
        user_ids = set()
        for source_field in field_mappings.keys():
            if source_field in data and data[source_field]:
                user_ids.add(str(data[source_field]))
                logger.debug(f"从字典中找到用户ID: {source_field}={data[source_field]}")
        
        logger.info(f"收集到的用户ID: {user_ids}")
        
        if not user_ids:
            logger.info("未找到需要查询的用户ID，跳过用户信息注入")
            return data
        
        # 批量获取用户姓名映射
        user_name_map = await get_user_name_map_by_ids(list(user_ids))
        
        logger.info(f"获取到的用户姓名映射: {user_name_map}")
        
        # 注入用户姓名
        injected_count = 0
        for source_field, target_field in field_mappings.items():
            if source_field in data and data[source_field]:
                user_id = str(data[source_field])
                user_name = user_name_map.get(user_id, "未知用户")
                data[target_field] = user_name
                injected_count += 1
                logger.debug(f"注入用户信息: {source_field}={user_id} -> {target_field}={user_name}")
        
        logger.info(f"单个数据用户信息注入完成，共注入 {injected_count} 个字段")
        return data