import os
from typing import Optional


class UserServiceConfig:
    """用户服务配置类"""
    
    def __init__(self, host: Optional[str] = None):
        """
        初始化用户服务配置
        
        Args:
            host: API服务的主机地址，如果不提供则从环境变量获取
        """
        self.host = host or os.getenv('HANHAI_USER_SERVICE_HOST', 'http://localhost:8080')
        
    def get_user_info_url(self) -> str:
        """获取用户信息查询API的完整URL"""
        return f"{self.host}/com.kingsoft.ai.hanhai.basematrix.system.api.RemoteUserService/querySimpleUserInfoByIds"


# 全局默认配置实例
default_user_service_config = UserServiceConfig()
