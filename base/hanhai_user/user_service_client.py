import logging
import json
from typing import List, Dict, Optional
import httpx
from .config import default_user_service_config, UserServiceConfig

logger = logging.getLogger("plaintext")


class UserServiceClient:
    """用户服务客户端"""
    
    def __init__(self, config: Optional[UserServiceConfig] = None):
        """
        初始化用户服务客户端
        
        Args:
            config: 用户服务配置，如果不提供则使用默认配置
        """
        self.config = config or default_user_service_config
        
    async def get_user_name_map_by_ids(self, user_ids: List[str]) -> Dict[str, str]:
        """
        根据用户ID列表获取用户名映射
        
        Args:
            user_ids: 用户ID列表
            
        Returns:
            用户ID到用户名的映射字典，格式：{user_id: user_name}
        """
        if not user_ids:
            logger.info("用户ID列表为空，返回空映射")
            return {}
            
        logger.info(f"开始查询用户信息，用户ID列表: {user_ids}")
        
        try:
            # 构建请求URL
            url = self.config.get_user_info_url()
            
            # 构建查询参数
            user_ids_param = json.dumps([int(uid) for uid in user_ids if uid.isdigit()])
            params = {"userIds": user_ids_param}
            
            logger.debug(f"请求URL: {url}")
            logger.debug(f"请求参数: {params}")
            
            # 发送HTTP请求
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                # 解析响应
                user_info_list = response.json()
                logger.debug(f"API响应: {user_info_list}")
                
                # 构建用户ID到用户名的映射
                user_name_map = {}
                for user_info in user_info_list:
                    user_id = str(user_info.get("userId"))
                    user_name = user_info.get("userName") or user_info.get("nickName", "未知用户")
                    user_name_map[user_id] = user_name
                    
                logger.info(f"成功获取用户信息映射: {user_name_map}")
                return user_name_map
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP请求失败: {e.response.status_code} - {e.response.text}")
            # 返回默认映射，避免影响业务流程
            return {uid: "未知用户" for uid in user_ids}
            
        except httpx.RequestError as e:
            logger.error(f"网络请求错误: {e}")
            # 返回默认映射，避免影响业务流程
            return {uid: "未知用户" for uid in user_ids}
            
        except Exception as e:
            logger.error(f"获取用户信息时发生未知错误: {e}")
            # 返回默认映射，避免影响业务流程
            return {uid: "未知用户" for uid in user_ids}


# 全局默认客户端实例
default_user_service_client = UserServiceClient()


async def get_user_name_map_by_ids(user_ids: List[str]) -> Dict[str, str]:
    """
    根据用户ID列表获取用户名映射的便捷函数
    
    Args:
        user_ids: 用户ID列表
        
    Returns:
        用户ID到用户名的映射字典，格式：{user_id: user_name}
    """
    return await default_user_service_client.get_user_name_map_by_ids(user_ids)
